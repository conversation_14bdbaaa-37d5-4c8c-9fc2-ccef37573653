server:
  port: 9100

spring:
  cache:
    default-ttl: 1200
    type: none
  application:
    # 应用名称
    name: hn-api-nacos
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  messages:
    #true使用本地包文件，false使用nacos配置
    localfile: true
    basename: i18n/messages
    encoding: UTF-8
    cacheMillis: 10000
  management:
    metrics:
      enable: false
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: ******************************************************************************=${spring.application.name}
    username: pg5gc_api_dev
    password: ENC(HvWjEY0JBB1ZGAe8SzOQGSjwyJO1KNvu)


management:
  metrics:
    enable: false

logging:
  level:
    root: info

# 接口地址配置
hnconfig:
  if-test: N
  query-nename-url: http://localhost:9100/api/test/get1
  query-csline-url: http://localhost:9100/api/test/get2
  query-dhwd-url: http://localhost:9100/api/test/get3

# Elasticsearch配置
elasticsearch:
  index:
    # 获取告警数据的索引
    alarmDataIndex: /alarm_data_index/_search
  server:
    tcpaddr: **************:9301,**************:9301,**************:9301
    httpaddr: **************:9201,**************:9201,**************:9201
    clustername: oss-test-es7
    protocol: http
    username: elastic
    # password: 'Ffcsoss!@#1115'
    password: 'ENC(gmHp61HVVUwqJba7CYPJ1jKg+s3XdbNa)'
    version: 7.x
#    url: http://**************:9211
