package com.ffcs.oss.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("传输设备告警查询条件")
public class ApiTransmissionAlarmQuery {
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", required = true)
    private String deviceName;

    /**
     * 设备IP
     */
    @ApiModelProperty(value = "设备IP")
    private String deviceIp;

    /**
     * 告警发生时间
     */
    @ApiModelProperty(value = "告警发生时间", required = true)
    private String startTime;

    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    private String specialty;

    /**
     * 操作类型：0-传输专业故障，1-具体故障类型
     */
    @ApiModelProperty(value = "操作类型：0-传输专业故障，1-具体故障类型", required = true)
    private Integer operationType;

    /**
     * 查询类型：0-图谱查询对端设备，1-查询传输设备告警
     */
    @ApiModelProperty(value = "查询类型：0-图谱查询对端设备，1-查询传输设备告警", required = true)
    private Integer searchType;

    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceType;
}
