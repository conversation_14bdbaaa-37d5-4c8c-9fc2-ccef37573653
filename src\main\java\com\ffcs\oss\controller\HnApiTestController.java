/**
 * Copyright (C), 2021, 中电福富信息科技有限公司
 * FileName: PortalExtraController
 * Author:   x<PERSON><PERSON><PERSON><PERSON>
 * Date:     2021/8/26 15:12
 */
package com.ffcs.oss.controller;

import com.ffcs.oss.domain.ApiCgyQuery;
import com.ffcs.oss.domain.ServiceResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: 接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/api/fault")
@Slf4j
@Api(tags = "场景故障根因查询 API", description = "场景故障根因查询 Rest API",value="HnApiTestController类")
public class HnApiTestController {
    @PostMapping("/getFaltRootstock")
    @ApiOperation("传输/数据故障根因查询")
    public ServiceResp getFaltRootstock(@RequestBody(required = true) ApiCgyQuery evt) {
        Map<String, Object> result = new HashMap<>();
        if (evt == null || StringUtils.isBlank(evt.getFultName())) {
            return ServiceResp.getInstance().error("定位不到故障根因");
        }
        String def1 = "服务端口断链";
        if (evt.getFultName().contains(def1) || def1.contains(evt.getFultName())) {
            result.put("result", "根据业务平台-云资源图谱和故障告警图谱分析：虚机*************;192.168.41.18ping异常");
            result.put("fultName", "虚机系统异常");
            return ServiceResp.getInstance().success(result);
        }
        String def2 = "OLT群障资源";
        if (evt.getFultName().contains(def2) || def2.contains(evt.getFultName())) {
            result.put("result", "根据OLT群障资源承载图谱和故障告警图谱分析：因美仁坡局站停电，导致OLT群障");
            result.put("fultName", "停电");
            return ServiceResp.getInstance().success(result);
        }
        String def3 = "大面积断站";
        if (evt.getFultName().contains(def3) || def3.contains(evt.getFultName())) {
            result.put("result", "根据大面积断站资源承载图谱和故障告警图谱分析；因海口义龙海联站址下挂10台BBU设备，因停电影响，导致基站大面积断站");
            result.put("fultName", "停电");
            return ServiceResp.getInstance().success(result);
        }
        return ServiceResp.getInstance().error("定位不到故障根因");
    }

    // /**
    //  * 13.	传输线路故障根因
    //  * 14.	数据端口故障根因
    //  *
    //  * @param evt
    //  * @return
    //  */
    // @PostMapping("/get1")
    // public String get1(@RequestBody ApiGzQuery evt) {
    //     String apiResult = "{\"head\":{\"resTime\":\"2024-09-27 00:35:24\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":[{\"stationCode\":\"HKO.ZSJ\",\"stationName\":\"东风\",\"stationId\":\"59000437\"}],\"success\":true}";
    //     return apiResult;
    // }
    //
    // @PostMapping("/get2")
    // public String get2(@RequestBody ApiGzQuery evt) {
    //     String apiResultGz = "{\"head\":{\"resTime\":\"2024-09-26 21:38:54\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":{\"total\":4,\"list\":[{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"电池欠压\",\"displayName\":\"光缆中断\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-FIBER_BREAK_POS\",\"displayName\":\"传输线路-FIBER_BREAK_POS\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_ERR\",\"displayName\":\"板卡故障_ERR\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_ERR\",\"displayName\":\"传输线路-HARD_ERR\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_BAD\",\"displayName\":\"板卡故障_BAD\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_BAD\",\"displayName\":\"传输线路-HARD_BAD\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"专业\",\"entityName\":\"传输\",\"displayName\":\"传输\",\"props\":[]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"rsEntity\":{\"rsTypeName\":\"专业-故障\",\"rsName\":\"传输-传输线路\",\"displayName\":\"传输-传输线路\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"}],\"pageNum\":1,\"pageSize\":10,\"size\":4,\"startRow\":0,\"endRow\":3,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"success\":true}";
    //     return apiResultGz;
    // }
    //
    // @PostMapping("/get3")
    // public String get3(@RequestBody Map<String, Object> evt) {
    //     String apiResult = "{\"head\":{\"resTime\":\"2024-09-26 21:38:54\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":{\"total\":4,\"list\":[{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"FIBER_BREAK_POS\",\"displayName\":\"光缆中断\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-FIBER_BREAK_POS\",\"displayName\":\"传输线路-FIBER_BREAK_POS\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_ERR\",\"displayName\":\"板卡故障_ERR\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_ERR\",\"displayName\":\"传输线路-HARD_ERR\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_BAD\",\"displayName\":\"板卡故障_BAD\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_BAD\",\"displayName\":\"传输线路-HARD_BAD\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"专业\",\"entityName\":\"传输\",\"displayName\":\"传输\",\"props\":[]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"rsEntity\":{\"rsTypeName\":\"专业-故障\",\"rsName\":\"传输-传输线路\",\"displayName\":\"传输-传输线路\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"}],\"pageNum\":1,\"pageSize\":10,\"size\":4,\"startRow\":0,\"endRow\":3,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"success\":true}";
    //     return apiResult;
    // }

}
