# 传输设备告警查询功能说明

## 功能概述

本功能实现了传输设备告警查询的需求，支持根据设备IP或设备名称查询图谱能力获取对端设备，并查询传输设备告警信息。

## 接口信息

### 接口地址
```
POST /api/fault/transmissionAlarmQuery
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deviceName | String | 是 | 设备名称 |
| deviceIp | String | 否 | 设备IP |
| startTime | String | 是 | 告警发生时间，格式：yyyy-MM-dd HH:mm:ss |
| specialty | String | 否 | 专业 |
| operationType | Integer | 是 | 操作类型：0-传输专业故障，1-具体故障类型 |
| searchType | Integer | 是 | 查询类型：0-图谱查询对端设备，1-查询传输设备告警 |
| deviceType | String | 否 | 设备类型 |

### 请求示例

#### 1. 图谱查询对端设备 (searchType=0)
```json
{
    "deviceName": "海口基站设备001",
    "deviceIp": "*************",
    "startTime": "2025-07-18 18:48:07",
    "specialty": "传输",
    "operationType": 1,
    "searchType": 0,
    "deviceType": "基站设备"
}
```

#### 2. 查询传输设备告警 (searchType=1)
```json
{
    "deviceName": "海口基站设备001",
    "deviceIp": "*************",
    "startTime": "2025-07-18 18:48:07",
    "specialty": "传输",
    "operationType": 1,
    "searchType": 1,
    "deviceType": "基站设备"
}
```

## 业务逻辑

### 1. searchType=0：图谱查询对端设备

1. **优先使用IP查询**：如果传入了deviceIp，优先调用图谱能力接口根据IP查询对端设备
2. **设备名称查询**：如果IP查询不到结果，则使用deviceName调用图谱能力接口查询对端设备
3. **无对端设备信息**：如果IP和设备名称都查询不到对端设备信息，返回错误码2，错误信息"无对端设备信息"

### 2. searchType=1：查询传输设备告警

1. **获取对端设备**：首先按照searchType=0的逻辑获取对端设备信息
2. **查询告警数据**：根据对端设备IP或设备名称、告警状态（clearancereportflag=0）、告警发生时间查询综告系统告警
3. **告警去重**：根据alarmSrc.SRC_ALARM_ID告警ID去重
4. **告警过滤**：根据专业和nacos配置的告警名称过滤告警
5. **结果返回**：根据operationType返回不同格式的分析结果

### 3. 告警过滤逻辑

- 如果nacos配置了告警名称且传入了specialty参数，则根据配置过滤告警
- 如果nacos未配置或specialty为空，则不进行过滤，返回全量告警
- 过滤规则：告警标题包含配置的告警名称即匹配

### 4. 故障类型判断逻辑

对于传输专业的告警，根据nacos配置的告警名称判断故障类型：

- **1,2,3类型告警**（配置的前3个告警名称）：对应故障类型为"光缆中断"
- **4,5类型告警**（配置的第4,5个告警名称）：对应故障类型为"板卡故障"
- **优先级**：如果同时匹配到1,2,3和4,5类型告警，优先返回"光缆中断"

## 返回结果

### 1. operationType=1时（具体故障类型）

```json
{
    "head": {
        "respCode": 0,
        "respMsg": "operate success",
        "resTime": "1753066341240"
    },
    "body": {
        "result": "根据资源承载图谱分析：海口基站设备001相关的基站设备传输设备海口核心传输设备001，相关的传输设备海口核心传输设备001在2025-07-21 10:35:50发生光纤中断告警；导致海口基站设备001发生故障；",
        "fultName": "光缆中断"
    }
}
```

### 2. operationType=0时（传输专业故障）

```json
{
    "head": {
        "respCode": 0,
        "respMsg": "operate success",
        "resTime": "1753066341240"
    },
    "body": {
        "result": "根据资源承载图谱分析：海口基站设备001相关的基站设备传输设备海口核心传输设备001，相关的传输设备海口核心传输设备001在2025-07-21 10:35:50发生故障；故障识别为传输专业故障；",
        "fultName": "传输专业故障"
    }
}
```

### 3. 未查询到告警数据时

```json
{
    "head": {
        "respCode": 0,
        "respMsg": "operate success",
        "resTime": "1753066341240"
    },
    "body": {
        "result": "根据资源承载图谱分析：海口基站设备001相关的基站设备传输设备海口核心传输设备001，该设备故障发生时段，相关的传输设备海口核心传输设备001运行正常未发生故障；下一步排查数据设备",
        "fultName": "下一步排查数据设备"
    }
}
```

### 4. 无对端设备信息时

```json
{
    "head": {
        "respCode": 2,
        "respMsg": "无对端设备信息",
        "resTime": "1753066341240"
    },
    "body": null
}
```

## 配置说明

### 1. 接口地址配置

在`application.yml`中配置图谱查询对端设备的接口地址：

```yaml
hnconfig:
  query-peer-device-url: http://your-graph-api/queryPeerDevice
```

### 2. Nacos告警名称配置

在nacos中配置`alarm-config.yml`文件：

```yaml
alarm:
  names:
    "传输":
      - "光纤中断告警"      # 1类型 - 光缆中断
      - "传输链路故障"      # 2类型 - 光缆中断  
      - "光缆断纤"         # 3类型 - 光缆中断
      - "板卡硬件故障"      # 4类型 - 板卡故障
      - "设备异常告警"      # 5类型 - 板卡故障
```

## 测试

项目提供了测试接口用于模拟外部图谱接口：

```
POST /api/fault/get6
```

该接口会返回模拟的对端设备数据，用于功能测试。

## 注意事项

1. 确保综告系统接口地址配置正确
2. 确保图谱查询对端设备接口地址配置正确
3. 根据实际业务需求调整nacos中的告警名称配置
4. 告警时间格式必须为：yyyy-MM-dd HH:mm:ss
5. 设备名称和设备IP至少提供一个，优先使用IP查询
