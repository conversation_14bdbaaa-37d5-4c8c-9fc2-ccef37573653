package com.ffcs.oss.service;

import com.ffcs.oss.domain.ApiTransmissionAlarmQuery;
import com.ffcs.oss.domain.ServiceResp;
import com.ffcs.oss.service.impl.HnApiServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import javax.annotation.Resource;

/**
 * 传输设备告警查询功能测试
 */
@SpringBootTest
@SpringJUnitConfig
public class TransmissionAlarmQueryTest {

    @Resource
    private IHnApiService hnApiService;

    /**
     * 测试图谱查询对端设备功能
     */
    @Test
    public void testQueryPeerDevice() throws Exception {
        ApiTransmissionAlarmQuery query = new ApiTransmissionAlarmQuery();
        query.setDeviceName("海口基站设备001");
        query.setDeviceIp("*************");
        query.setStartTime("2025-07-18 18:48:07");
        query.setSpecialty("传输");
        query.setOperationType(1);
        query.setSearchType(0); // 图谱查询对端设备
        query.setDeviceType("基站设备");

        ServiceResp result = hnApiService.transmissionAlarmQuery(query);
        
        System.out.println("图谱查询对端设备结果：" + result);
        assert result != null;
    }

    /**
     * 测试查询传输设备告警功能 - 具体故障类型
     */
    @Test
    public void testQueryTransmissionAlarmSpecific() throws Exception {
        ApiTransmissionAlarmQuery query = new ApiTransmissionAlarmQuery();
        query.setDeviceName("海口基站设备001");
        query.setDeviceIp("*************");
        query.setStartTime("2025-07-18 18:48:07");
        query.setSpecialty("传输");
        query.setOperationType(1); // 具体故障类型
        query.setSearchType(1); // 查询传输设备告警
        query.setDeviceType("基站设备");

        ServiceResp result = hnApiService.transmissionAlarmQuery(query);
        
        System.out.println("查询传输设备告警结果（具体故障类型）：" + result);
        assert result != null;
    }

    /**
     * 测试查询传输设备告警功能 - 传输专业故障
     */
    @Test
    public void testQueryTransmissionAlarmProfessional() throws Exception {
        ApiTransmissionAlarmQuery query = new ApiTransmissionAlarmQuery();
        query.setDeviceName("海口基站设备001");
        query.setDeviceIp("*************");
        query.setStartTime("2025-07-18 18:48:07");
        query.setSpecialty("传输");
        query.setOperationType(0); // 传输专业故障
        query.setSearchType(1); // 查询传输设备告警
        query.setDeviceType("基站设备");

        ServiceResp result = hnApiService.transmissionAlarmQuery(query);
        
        System.out.println("查询传输设备告警结果（传输专业故障）：" + result);
        assert result != null;
    }

    /**
     * 测试参数校验
     */
    @Test
    public void testParameterValidation() throws Exception {
        ApiTransmissionAlarmQuery query = new ApiTransmissionAlarmQuery();
        // 缺少必填参数
        query.setDeviceName("海口基站设备001");
        // 缺少startTime、searchType、operationType

        ServiceResp result = hnApiService.transmissionAlarmQuery(query);
        
        System.out.println("参数校验结果：" + result);
        assert result != null;
        assert result.getHead().getRespCode() == -1; // 应该返回错误
    }

    /**
     * 测试仅使用设备名称查询
     */
    @Test
    public void testQueryByDeviceNameOnly() throws Exception {
        ApiTransmissionAlarmQuery query = new ApiTransmissionAlarmQuery();
        query.setDeviceName("海口基站设备001");
        // 不设置deviceIp
        query.setStartTime("2025-07-18 18:48:07");
        query.setSpecialty("传输");
        query.setOperationType(1);
        query.setSearchType(1);
        query.setDeviceType("基站设备");

        ServiceResp result = hnApiService.transmissionAlarmQuery(query);
        
        System.out.println("仅使用设备名称查询结果：" + result);
        assert result != null;
    }
}
