# Nacos配置示例 - 告警名称配置
# 配置文件名：alarm-config.yml
# 在nacos中创建此配置文件，用于配置不同专业对应的告警名称

alarm:
  names:
    # 无线专业的告警名称配置
    "无线(中兴5G)":
      - "DU小区退服"
      - "RRU断链"
      - "基站掉电"
      - "传输中断"
    
    # 传输专业的告警名称配置
    "传输":
      - "光缆中断"
      - "设备断电"
      - "链路故障"
      - "端口异常"
    
    # 动环专业的告警名称配置
    "动环":
      - "停电"
      - "欠压"
      - "高温"
      - "湿度异常"
    
    # 核心网专业的告警名称配置
    "核心网":
      - "服务器故障"
      - "网络中断"
      - "数据库异常"
      - "应用服务异常"

# 注意：
# 1. 专业名称需要与实际传入的specialty参数完全匹配
# 2. 告警名称支持模糊匹配，只要告警标题包含配置的名称即可
# 3. 如果某个专业未配置或配置为空，则该专业的告警不会被过滤
# 4. 如果整个配置为空或specialty参数为空，则不进行过滤，返回全量告警
