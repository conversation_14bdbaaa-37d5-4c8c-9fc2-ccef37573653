# Nacos配置示例 - 告警名称配置
# 配置文件名：alarm-config.yml
# 在nacos中创建此配置文件，用于配置不同专业对应的告警名称

alarm:
  names:
    # 无线专业的告警名称配置
    "无线(中兴5G)":
      - "DU小区退服"
      - "RRU断链"
      - "基站掉电"
      - "传输中断"
    
    # 传输专业的告警名称配置
    "传输":
      - "光纤中断告警"      # 1类型 - 光缆中断
      - "传输链路故障"      # 2类型 - 光缆中断
      - "光缆断纤"         # 3类型 - 光缆中断
      - "板卡硬件故障"      # 4类型 - 板卡故障
      - "设备异常告警"      # 5类型 - 板卡故障
    
    # 动环专业的告警名称配置
    "动环":
      - "停电"
      - "欠压"
      - "高温"
      - "湿度异常"
    
    # 核心网专业的告警名称配置
    "核心网":
      - "服务器故障"
      - "网络中断"
      - "数据库异常"
      - "应用服务异常"

# 注意：
# 1. 专业名称需要与实际传入的specialty参数完全匹配
# 2. 告警名称支持模糊匹配，只要告警标题包含配置的名称即可
# 3. 如果某个专业未配置或配置为空，则该专业的告警不会被过滤
# 4. 如果整个配置为空或specialty参数为空，则不进行过滤，返回全量告警
# 5. 对于传输专业：
#    - 1,2,3类型告警（光纤中断告警、传输链路故障、光缆断纤）对应故障类型：光缆中断
#    - 4,5类型告警（板卡硬件故障、设备异常告警）对应故障类型：板卡故障
#    - 如果同时匹配到1,2,3和4,5类型告警，优先返回：光缆中断
