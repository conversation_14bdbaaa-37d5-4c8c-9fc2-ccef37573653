/**
 * Copyright (C), 2021, 中电福富信息科技有限公司
 * FileName: PortalExtraService
 * Author:   xu<PERSON><PERSON><PERSON>
 * Date:     2021/8/26 15:06
 */
package com.ffcs.oss.service;

import com.ffcs.oss.domain.ApiCarrierQuery;
import com.ffcs.oss.domain.ApiCloudQuery;
import com.ffcs.oss.domain.ApiDhQuery;
import com.ffcs.oss.domain.ApiGzQuery;
import com.ffcs.oss.domain.ServiceResp;

import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2021/8/26  15:06
 */
public interface IHnApiService {

    /**
     * 13.	传输线路故障根因
     * 14.	数据端口故障根因
     *
     * @param evt
     * @return
     */
    ServiceResp ckTransDataFault(ApiGzQuery evt) throws Exception;

    /**
     * 15.	动环温湿度故障根因
     *
     * @param evt
     * @return
     * @throws Exception
     */
    ServiceResp ckRotatingRingFault(ApiDhQuery evt) throws Exception;

    /**
     * 18.	云根因定位
     *
     * @param evt
     * @return
     * @throws Exception
     */
    ServiceResp cloudCauseFault(ApiCloudQuery evt) throws Exception;

    /**
     * 19.	承载故障根因分析
     *
     * @param evt
     * @return
     * @throws Exception
     */
    ServiceResp carrierFaultAnalysis(ApiCarrierQuery evt) throws Exception;


}
