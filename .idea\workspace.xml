<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="466932b6-c822-4708-b0a5-f2278d358880" name="Changes" comment="【新增】查询局站动环告警">
      <change afterPath="$PROJECT_DIR$/database-scripts.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/nacos-alarm-config-example.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/承载故障根因分析功能说明.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/postman-collection.json" beforeDir="false" afterPath="$PROJECT_DIR$/postman-collection.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/domain/ApiCarrierQuery.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/domain/ApiCarrierQuery.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/service/impl/HnApiServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ffcs/oss/service/impl/HnApiServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/ffcs/oss/service/CarrierFaultAnalysisTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/ffcs/oss/service/CarrierFaultAnalysisTest.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="D:/Program Files/Java/sx_maven/apache-maven-3.6.0" />
        <option name="userSettingsFile" value="D:\Program Files\Java\sx_maven\apache-maven-3.6.0\conf\settings_ffcs.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30aMVtmbexNA0TiED4ciFHD4jqb" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "reference.projectsettings.compiler.javacompiler",
    "spring.configuration.checksum": "b4ad4240c4194df00a0c2c787eefd370",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="CloudDemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="hn-api-nacos" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ffcs.oss.CloudDemoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="466932b6-c822-4708-b0a5-f2278d358880" name="Changes" comment="" />
      <created>1753861258969</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753861258969</updated>
      <workItem from="1753861261150" duration="2060000" />
      <workItem from="1753964791078" duration="9189000" />
    </task>
    <task id="LOCAL-00001" summary="【新增】查询局站动环告警">
      <option name="closed" value="true" />
      <created>1754035774328</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754035774328</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/main" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="【新增】查询局站动环告警" />
    <option name="LAST_COMMIT_MESSAGE" value="【新增】查询局站动环告警" />
  </component>
</project>