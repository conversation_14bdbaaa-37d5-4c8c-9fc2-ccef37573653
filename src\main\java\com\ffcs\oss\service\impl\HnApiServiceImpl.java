
package com.ffcs.oss.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ffcs.oss.config.CommonConstant;
import com.ffcs.oss.config.ElasticsearchConfig;
import com.ffcs.oss.config.EsDslConstant;
import com.ffcs.oss.config.HnConfigProperties;
import com.ffcs.oss.domain.*;
import com.ffcs.oss.es.utils.EsHighClientUtil;
import com.ffcs.oss.es.utils.PropertyConfig;
import com.ffcs.oss.mapper.HnApiMapper;
import com.ffcs.oss.service.IHnApiService;
import com.ffcs.oss.utils.OkHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import com.ffcs.oss.config.AlarmNameConfig;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2021/8/26  15:06
 */
@Service
@Slf4j
public class HnApiServiceImpl implements IHnApiService {

    // 定义日期时间格式
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final List<String> ALARM_NAME = new ArrayList<>(Arrays.asList(
            "承载网管系统告警 网元断链",
            "NE_COMMU_BREAK",
            "NODE DOWN"
    ));

    @Autowired
    private HnConfigProperties configProperties;

    @Autowired
    private ElasticsearchConfig elasticsearchConfig;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${elasticsearch.index.alarm_data_index:/alarm_data_index/_search}")
    private String alarmDataIndex;

    @Autowired
    private HnApiMapper hnApiMapper;

    @Autowired
    private AlarmNameConfig alarmNameConfig;

    private RestHighLevelClient esClient;

    private void initEsCilent() throws Exception {
        // 初始化esClient
        esClient = EsHighClientUtil.getClient(JSONObject.parseObject(JSONObject.toJSONString(this.elasticsearchConfig), PropertyConfig.class));
    }

    @PostConstruct
    public void init() {
        try {
            log.info("初始化es客户端");
            // 初始化esClient
            initEsCilent();
        } catch (Exception e) {
            log.error("es init failed", e);
        }
    }

    private String buildEsDsl(String alarmStatus, String major, String equipmentName, String zlparam, String qyparam) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 计算前30分钟的时间点
        LocalDateTime thirtyMinutesAgo = now.minusMinutes(120);

        String startTime = now.format(formatter);
        String rusultTime = thirtyMinutesAgo.format(formatter);

        String dsl = queryDsl.replace("#status#", alarmStatus).replace("#equipmentName#", equipmentName)
                .replace("#major#", major)
                .replace("#zlparam#", zlparam)
                .replace("#qyparam#", qyparam)
                .replace("#startTime#", startTime)
                .replace("#rusultTime#", rusultTime);
        return dsl;
    }

    private String buildEsDslAlarm(String alarmStatus, String equipmentName) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 计算前30分钟的时间点
        LocalDateTime thirtyMinutesAgo = now.minusMinutes(120);

        String startTime = now.format(formatter);
        String rusultTime = thirtyMinutesAgo.format(formatter);

        String dsl = queryDslAlarm.replace("#status#", alarmStatus).replace("#equipmentName#", equipmentName)
                                .replace("#startTime#", startTime)
                                .replace("#rusultTime#", rusultTime);
        return dsl;
    }

    private String buildEsDslJz(String alarmStatus, String major, String equipmentName) {
        String dsl = queryDslJz.replace("#status#", alarmStatus).replace("#major#", major).replace("#equipmentName#", equipmentName);
        return dsl;
    }

    private String buildEsDslCellSiteQry(String cellSite, String alarmStatus){
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 计算前30分钟的时间点
        LocalDateTime thirtyMinutesAgo = now.minusMinutes(120);

        String startTime = now.format(formatter);
        String rusultTime = thirtyMinutesAgo.format(formatter);
        String dsl = EsDslConstant.QUERY_CELL_SITE_ALARM_DSL.replace("#status#", alarmStatus)
                .replace("#cellSite#", cellSite)
                .replace("#startTime#", startTime)
                .replace("#rusultTime#", rusultTime);
        return dsl;
    }

    private String buildEsDslCloudRootCause(String startTime, String resultTime,String alarmStatus,String alarmTitles){
        String dsl = EsDslConstant.POSITIONING_CLOUD_CAUSE_FAULT_DSL.replace("#startTime#", startTime)
                .replace("#rusultTime#", resultTime)
                .replace("#status#", alarmStatus)
                .replace("#alarmTitles#",alarmTitles);
        return dsl;
    }

    private String queryDsl = "{\n" +
            "  \"query\": {\n" +
            "    \"bool\": {\n" +
            "      \"must\": [\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"alarmStatus\": \"#status#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"major\": \"#major#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"wildcard\": {\n" +
            "            \"equipmentName\": \"*#equipmentName#*\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"bool\": {\n" +
            "            \"must\": [\n" +
            "              {\n" +
            "                \"wildcard\": {\n" +
            "                  \"alarmTitle\": \"*#qyparam#*\"\n" +
            "                }\n" +
            "              },\n" +
            "              {\n" +
            "                \"wildcard\": {\n" +
            "                  \"alarmTitle\": \"*#zlparam#*\"\n" +
            "                }\n" +
            "              }\n" +
            "            ]\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"range\": {\n" +
            "            \"eventTime\": {\n" +
            "              \"gte\": \"" + "#rusultTime#" + "\",\n" +
            "              \"lte\": \"" + "#startTime#" + "\",\n" +
            "              \"format\": \"yyyy-MM-dd HH:mm:ss\"\n" +
            "            }\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  }\n" +
            "}\n";

    private String queryDslAlarm = "{\n" +
            "  \"query\": {\n" +
            "    \"bool\": {\n" +
            "      \"must\": [\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"alarmStatus\": \"#status#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"equipmentName\": \"#equipmentName#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"range\": {\n" +
            "            \"eventTime\": {\n" +
            "              \"gte\": \"" + "#rusultTime#" + "\",\n" +
            "              \"lte\": \"" + "#startTime#" + "\",\n" +
            "              \"format\": \"yyyy-MM-dd HH:mm:ss\"\n" +
            "            }\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  }\n" +
            "}\n";
    private String queryDslJz = "{\n" +
            "  \"query\": {\n" +
            "    \"bool\": {\n" +
            "      \"must\": [\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"alarmStatus\": \"#status#\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"term\": {\n" +
            "            \"major\": \"#major#\"\n" +
            "          }\n" +
            "        },\n" +
            "       {\n" +
            "          \"bool\": {\n" +
            "            \"should\": [\n" +
            "              {\n" +
            "                \"wildcard\": {\n" +
            "                  \"equipmentName\": \"*#equipmentName#*\"\n" +
            "                }\n" +
            "              }\n" +
            "            ],\n" +
            "            \"minimum_should_match\": 1\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  }\n" +
            "}\n";

    @Override
    public ServiceResp ckTransDataFault(ApiGzQuery evt) throws Exception {
        if (evt.getType() == null) {
            return ServiceResp.getInstance().error("参数错误:分类不能为空");
        }
        if (StringUtils.isBlank(evt.getNeName())) {
            return ServiceResp.getInstance().error("参数错误：设备名称不能为空");
        }
        if (evt.getType().equals(CommonConstant.TYPE_DYNAMIC)) {
            // 根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码
//            if (StringUtils.isBlank(evt.getAlarmName()) || !ALARM_NAME.contains(evt.getAlarmName()) ){
//                log.info("13接口,入参:{},根据告警名称判定为直接执行逻辑2...",JSONObject.toJSONString(evt));
//                return matchAlarmByNeName(evt);
//            }
            String neCode = "";
            String neName = "";
            String apiResult;
            if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
                apiResult = "{\"head\":{\"resTime\":\"2024-09-27 00:35:24\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":[{\"stationCode\":\"QHA.DFJ\",\"stationName\":\"东风\",\"stationId\":\"59000437\"}],\"success\":true}";
            } else {
                // 根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码
                Map<String, Object> params = new HashMap<>();
                params.put("tgName", CommonConstant.TG_NAME);
                params.put("deviceName", evt.getNeName());
                log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{},入参:{}", configProperties.getQueryNenameUrl(), JSONObject.toJSONString(params));
                apiResult = OkHttpUtils.doPost(configProperties.getQueryNenameUrl(), JSON.toJSONString(params));
                log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{}，返回内容:{}", configProperties.getQueryNenameUrl(), apiResult);
            }
            if (apiResult == null) {
                log.error("根据设备名称，调用图谱能力，查询设备所属局站,返回数据异常,入参:{}", JSONObject.toJSONString(evt));
                Map<String, Object> resultStr = buildNoResult(evt.getType());
                return ServiceResp.getInstance().success(resultStr);
            }
            JSONObject map = JSONObject.parseObject(apiResult);
            if (map == null || !map.getBoolean("success")) {
                log.error("根据设备名称，调用图谱能力，查询设备所属局站,返回数据异常,入参:{}", JSONObject.toJSONString(evt));
                Map<String, Object> resultStr = buildNoResult(evt.getType());
                return ServiceResp.getInstance().success(resultStr);
            }
            JSONArray array = map.getJSONArray("body");
            if (array != null && !array.isEmpty()) {
                JSONObject jsonObject = array.getJSONObject(0);
                neCode = jsonObject.getString("stationCode");
                neName = jsonObject.getString("stationName");
            }
            if (StringUtils.isBlank(neCode)) {
                log.error("根据设备名称，调用图谱能力，查询设备所属局站，未获取到内容,入参:{}", JSONObject.toJSONString(evt));
                Map<String, Object> resultStr = buildNoResult(evt.getType());
                return ServiceResp.getInstance().success(resultStr);
            }
            // 根据局站编码查询es库中设备名称是否有包含局站编码的告警，且告警标题包含停电、欠压或高温，并且告警状态alarmStatus为0，且专业为9
            // 构建dsl语句
            String replace = buildEsDsl(CommonConstant.ALARM_STATUS, CommonConstant.MAJOR, neCode, CommonConstant.ZL,CommonConstant.QY);
            log.info("根据局站编码查询es库中设备名称dsl:{}", replace);
            // 调用es接口
            EsHighClientUtil esHighClientUtil = new EsHighClientUtil();
            String tempEs = esHighClientUtil.dslHttp(alarmDataIndex, replace, HttpMethod.POST.name(), esClient);
            log.info("根据局站编码查询es库中设备名称返回:{}", tempEs);
            JSONObject esJzJson = JSON.parseObject(tempEs);
            JSONObject hits = esJzJson.getJSONObject("hits");
            if (hits == null) {
                // 为null应该是查询异常了
                log.error("根据局站编码查询es库中设备名称是否有包含局站编码的告警，未获取到内容,入参:{}", JSONObject.toJSONString(evt));
                Map<String, Object> resultStr = buildNoResult(evt.getType());
                return ServiceResp.getInstance().success(resultStr);
            }
            Integer total = hits.getJSONObject("total").getInteger("value");
            if (total != null || total > 0) {
                JSONArray esDatas = hits.getJSONArray("hits");
                if (esDatas != null && !esDatas.isEmpty()) {
                    Map<String, Object> resultMap = new HashMap<>();
                    // 询到返回，“result”结论返回
                    String result = CommonConstant.RESULT_DH_TD_FAULT.replace("#neName#", neName);
                    resultMap.put("result", result);
                    resultMap.put("fultName", CommonConstant.TD);
                    return ServiceResp.getInstance().success(resultMap);
                }
            }
            // 逻辑2
            return matchAlarmByNeName(evt);
        } else if (evt.getType().equals(CommonConstant.TYPE_MAJOR)) {
            // 将14接口合并到13;这边进行type=2即数据端口相关逻辑处理
            // 查询库表, 获取cell_site基站站址，基站站址可能为空，若为空进行逻辑2查询
            List<String> cellSites = hnApiMapper.getCellSite(evt.getNeName());
            log.info("入参:{},type=2,进入接口14流程...根据入参neName查询基站站址,查询结果:{}",JSONObject.toJSONString(evt),JSONObject.toJSONString(cellSites));
            if (cellSites != null && cellSites.size() > 0){
                // 不为空
                if (cellSites.size() == 1){
                    String qryCellSiteDsl = buildEsDslCellSiteQry(cellSites.get(0), CommonConstant.ALARM_STATUS);
                    log.info("根据基站站址查询es库中告警dsl:{}", qryCellSiteDsl);
                    EsHighClientUtil esHighClientUtil = new EsHighClientUtil();
                    String tempResult = esHighClientUtil.dslHttp(alarmDataIndex, qryCellSiteDsl, HttpMethod.POST.name(), esClient);
                    log.info("根据基站站址查询es库中告警返回:{}", tempResult);
                    JSONObject esCellSiteJson = JSON.parseObject(tempResult);
                    JSONObject hitsCellSite = esCellSiteJson.getJSONObject("hits");
                    if (hitsCellSite == null) {
                        log.error("根据基站站址查询es库中告警,未获取到内容,入参:{}", JSONObject.toJSONString(evt));
                        Map<String, Object> resultStr = buildNoResult(evt.getType());
                        return ServiceResp.getInstance().success(resultStr);
                    }
                    Integer total = hitsCellSite.getJSONObject("total").getInteger("value");
                    if (total != null && total > 0) {
                        // 若查询到返回
                        Map<String, Object> resultMap = new HashMap<>();
                        resultMap.put("result", CommonConstant.RESULT_CELL_SITE_TD_FAULT.replace("#siteSite#", cellSites.get(0)));
                        resultMap.put("fultName", CommonConstant.TD);
                        return ServiceResp.getInstance().success(resultMap);
                    }
                } else if (cellSites.size() > 1){
                    // 多个基站站址
                    log.error("根据设别名称查询到多个站址!入参:{}", JSONObject.toJSONString(evt));
                    Map<String, Object> resultStr = buildNoResult(evt.getType());
                    return ServiceResp.getInstance().success(resultStr);
                }
            }
            // 14接口 未找到站址 逻辑2查询
            return matchAlarmByNeName(evt);
        } else {
            return ServiceResp.getInstance().error("参数错误：没有匹配的类型!");
        }
    }

    private ServiceResp matchAlarmByNeName(ApiGzQuery evt) throws Exception {
        // 若查询不到： 根据设备名称（等于）查询es库中告警，告警状态alarmStatus为0；根据查询到的告警名称（完成匹配）
        // 构建dsl语句
        String replaceDsl = buildEsDslAlarm(CommonConstant.ALARM_STATUS, evt.getNeName());
        log.info("根据设备名称查询es库中告警dsl:{}", replaceDsl);
        // 调用es接口
        EsHighClientUtil esHighClientUtil = new EsHighClientUtil();
        String tempEsAlarm = esHighClientUtil.dslHttp(alarmDataIndex, replaceDsl, HttpMethod.POST.name(), esClient);
        log.info("根据设备名称查询es库中告警返回:{}", tempEsAlarm);
        JSONObject tempJsonAlarm = JSON.parseObject(tempEsAlarm);
        JSONObject hitsAlarm = tempJsonAlarm.getJSONObject("hits");
        if (hitsAlarm == null) {
            log.error("根据局站编码查询es库中设备名称是否有包含局站编码的告警，未获取到内容;入参:{}", JSONObject.toJSONString(evt));
            Map<String, Object> resultStr = buildNoResult(evt.getType());
            return ServiceResp.getInstance().success(resultStr);
        }
        Integer totalAlarm = hitsAlarm.getJSONObject("total").getInteger("value");
        if (totalAlarm == null || totalAlarm == 0) {
            log.error("es返回数据为0;入参:{}", JSONObject.toJSONString(evt));
            Map<String, Object> resultStr = buildNoResult(evt.getType());
            return ServiceResp.getInstance().success(resultStr);
        }
        JSONArray esDatasAlarm = hitsAlarm.getJSONArray("hits");
        if (esDatasAlarm != null && esDatasAlarm.isEmpty()) {
            log.debug("13、14接口,es未查到数据,入参:{}", JSONObject.toJSONString(evt));
            Map<String, Object> resultStr = buildNoResult(evt.getType());
            return ServiceResp.getInstance().success(resultStr);
        }
        // 调用故障告警图谱接口获取到的故障根因
        String apiResultGz;
        if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
            apiResultGz = "{\"head\":{\"resTime\":\"2024-09-26 21:38:54\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":{\"total\":4,\"list\":[{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"FIBER_BREAK_POS\",\"displayName\":\"光缆中断\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-FIBER_BREAK_POS\",\"displayName\":\"传输线路-FIBER_BREAK_POS\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_ERR\",\"displayName\":\"板卡故障_ERR\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_ERR\",\"displayName\":\"传输线路-HARD_ERR\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_BAD\",\"displayName\":\"板卡故障_BAD\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_BAD\",\"displayName\":\"传输线路-HARD_BAD\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"专业\",\"entityName\":\"传输\",\"displayName\":\"传输\",\"props\":[]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"rsEntity\":{\"rsTypeName\":\"专业-故障\",\"rsName\":\"传输-传输线路\",\"displayName\":\"传输-传输线路\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"}],\"pageNum\":1,\"pageSize\":10,\"size\":4,\"startRow\":0,\"endRow\":3,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"success\":true}";
        } else {
            Map<String, Object> params = new HashMap<>();
            if (CommonConstant.TYPE_MAJOR.equals(evt.getType())) {
                params.put("entityName", "数据端口");
            } else {
                params.put("entityName", "传输线路");
            }
            params.put("tgName", "故障图谱/故障告警图谱");
            params.put("entityTypeName", "故障");
            params.put("pageNo", "1");
            params.put("pageSize", "10");
            log.info("调用故障告警图谱接口获取到的故障根因URL:{},入参:{}", configProperties.getQueryCslineUrl(), JSONObject.toJSONString(params));
            apiResultGz = OkHttpUtils.doPost(configProperties.getQueryCslineUrl(), JSON.toJSONString(params));
            log.info("调用故障告警图谱接口获取到的故障根因URL:{}，返回内容:{}", configProperties.getQueryCslineUrl(), apiResultGz);
        }
        if (apiResultGz == null) {
            log.error("调用故障告警图谱接口获取到的故障根因,返回数据异常,入参:{}", JSONObject.toJSONString(evt));
            Map<String, Object> resultStr = buildNoResult(evt.getType());
            return ServiceResp.getInstance().success(resultStr);
        }
        JSONObject apiMapGz = JSON.parseObject(apiResultGz);
        if (apiMapGz == null || !apiMapGz.getBoolean("success")) {
            log.error("调用故障告警图谱接口获取到的故障根因,接口调用返回异常,入参:{},回参:{}", JSONObject.toJSONString(evt),apiMapGz);
            Map<String, Object> resultStr = buildNoResult(evt.getType());
            return ServiceResp.getInstance().success(resultStr);
        }
        JSONObject apiJson = apiMapGz.getJSONObject("body");
        if (apiJson == null || apiJson.isEmpty()) {
            Map<String, Object> resultStr = buildNoResult(evt.getType());
            return ServiceResp.getInstance().success(resultStr);
        }
        JSONArray apiarray = apiJson.getJSONArray("list");
        if (apiarray == null || apiarray.isEmpty()) {
            Map<String, Object> resultStr = buildNoResult(evt.getType());
            return ServiceResp.getInstance().success(resultStr);
        }
        String gzEntityName = "";
        String tempEarliestTime = "";
        for (Object obj : esDatasAlarm) {
            // 遍历ES查询结果
            JSONObject esJsonHit = JSON.parseObject(obj.toString());
            JSONObject esJson = esJsonHit.getJSONObject("_source");
            for (Object alarmObj : apiarray) {
                // 遍历接口查询结果
                JSONObject jsonAlarm = JSON.parseObject(alarmObj.toString());
                JSONObject zEntity = jsonAlarm.getJSONObject("zEntity");
                if (zEntity.getString("entityName").equals(esJson.getString("alarmTitle"))){
                    // 告警名称与传输根的告警名称
                    if (StringUtils.isBlank(tempEarliestTime)){
                        // 第一个时间
                        tempEarliestTime = esJson.getString("eventTime");
                        gzEntityName = zEntity.getString("displayName");
                    }else {
                        // 将tempEarliestTime转为时间进行比较
                        LocalDateTime tempEarliestDate = LocalDateTime.parse(tempEarliestTime, formatter);
                        LocalDateTime alarmEventTime = LocalDateTime.parse(esJson.getString("eventTime"), formatter);
                        if (alarmEventTime.isBefore(tempEarliestDate)){
                            // 当前匹配的告警时间更早
                            tempEarliestTime = esJson.getString("eventTime");
                            gzEntityName = zEntity.getString("displayName");
                        }
                    }
                }
            }
        }
        if (StringUtils.isBlank(gzEntityName)) {
            Map<String, Object> resultStr = buildNoResult(evt.getType());
            return ServiceResp.getInstance().success(resultStr);
        }
        Map<String, Object> resultMap = new HashMap<>();
        if (evt.getType().equals(CommonConstant.TYPE_MAJOR)){
            resultMap.put("result", CommonConstant.RESULT_SJ_PORT_FAULT.replace("#gzEntityName#", gzEntityName));
        }else {
            resultMap.put("result", CommonConstant.RESULT_CS_FAULT.replace("#gzEntityName#", gzEntityName));
        }
        resultMap.put("fultName", gzEntityName);
        return ServiceResp.getInstance().success(resultMap);
    }

    private Map<String, Object> buildNoResult(Integer type){
        Map<String, Object> resultMap = new HashMap<>();
        if (CommonConstant.TYPE_DYNAMIC.equals(type)){
            resultMap.put("result", CommonConstant.NO_RESULT_CS);
        }else {
            resultMap.put("result", CommonConstant.NO_RESULT_DK);
        }
        resultMap.put("fultName", CommonConstant.FAULTNAME_GLZD);
        return resultMap;
    }

    @Override
    public ServiceResp ckRotatingRingFault(ApiDhQuery evt) throws Exception {
        if (StringUtils.isBlank(evt.getNeName())) {
            return ServiceResp.getInstance().error("参数错误：设备名称不能为空");
        }
        // 改用直接传参 241028 不通过图谱能力查询局站编码
        if (StringUtils.isBlank(evt.getStationCode())){
            return ServiceResp.getInstance().error("参数错误：局站编码不能为空");
        }
        // 根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码
//        String neCode = "";
//        String apiNeResult;
//        if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
//            apiNeResult = "{\"head\":{\"resTime\":\"2024-09-27 00:35:24\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":[{\"stationCode\":\"QHA.DFJ\",\"stationName\":\"东风\",\"stationId\":\"59000437\"}],\"success\":true}";
//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("tgName", CommonConstant.TG_NAME);
//            params.put("deviceName", evt.getNeName());
//            log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{},入参:{}", configProperties.getQueryNenameUrl(), JSONObject.toJSONString(params));
//            apiNeResult = OkHttpUtils.doPost(configProperties.getQueryNenameUrl(), JSON.toJSONString(params));
//            log.info("根据设备名称，调用图谱能力，查询设备所属局站，获取局站编码URL:{}，返回内容:{}", configProperties.getQueryNenameUrl(), apiNeResult);
//        }
//        if (apiNeResult == null) {
//            return ServiceResp.getInstance().error("根据设备名称，调用图谱能力，查询设备所属局站,返回数据异常");
//        }
//        JSONObject neMap = JSONObject.parseObject(apiNeResult);
//        if (neMap == null || !neMap.getBoolean("success")) {
//            return ServiceResp.getInstance().error("根据设备名称，调用图谱能力，查询设备所属局站,返回数据异常");
//        }
//        JSONArray neArray = neMap.getJSONArray("body");
//        if (neArray != null && !neArray.isEmpty()) {
//            JSONObject jsonObject = neArray.getJSONObject(0);
//            // 局站编码
//            neCode = jsonObject.getString("stationCode");
//        }
//        if (StringUtils.isBlank(neCode)) {
//            return ServiceResp.getInstance().error("根据设备名称，调用图谱能力，查询设备所属局站，未获取到内容");
//        }
        // 根据局站编码查询es库中设备名称是否有包含局站编码的告警，且告警标题包含停电、欠压或高温，并且告警状态alarmStatus为1，且专业为9
        // 调用es接口
        EsHighClientUtil esHighClientUtil = new EsHighClientUtil();
        // 构建dsl语句
        String stationCode = evt.getStationCode();
        String replace = buildEsDslJz(CommonConstant.ALARM_STATUS, CommonConstant.MAJOR, stationCode);
        log.info("根据局站编码查询es库中设备Dsl:{}", replace);
        String tempEs = esHighClientUtil.dslHttp(alarmDataIndex, replace, HttpMethod.POST.name(), esClient);
        log.info("根据局站编码查询es库中设备返回:{}", tempEs);
        JSONObject tempJson = JSON.parseObject(tempEs);
        JSONObject hits = tempJson.getJSONObject("hits");
        if (hits == null) {
            return ServiceResp.getInstance().error(2, CommonConstant.NO_FAULT_ROOTCAUSE);
        }
        Integer total = hits.getJSONObject("total").getInteger("value");
        if (total == null || total == 0) {
            return ServiceResp.getInstance().error(2, CommonConstant.NO_FAULT_ROOTCAUSE);
        }
        JSONArray esDatas = hits.getJSONArray("hits");
        if (esDatas == null || esDatas.isEmpty()) {
            return ServiceResp.getInstance().error(2, CommonConstant.NO_FAULT_ROOTCAUSE);
        }
        // 调用故障告警图谱接口获取到的故障根因
        String apiResult;
        if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
            apiResult = "{\"head\":{\"resTime\":\"2024-09-26 21:38:54\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":{\"total\":4,\"list\":[{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"FIBER_BREAK_POS\",\"displayName\":\"光缆中断\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-FIBER_BREAK_POS\",\"displayName\":\"传输线路-FIBER_BREAK_POS\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_ERR\",\"displayName\":\"板卡故障_ERR\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_ERR\",\"displayName\":\"传输线路-HARD_ERR\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"HARD_BAD\",\"displayName\":\"板卡故障_BAD\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"根\"}]},\"rsEntity\":{\"rsTypeName\":\"故障\",\"rsName\":\"传输线路-HARD_BAD\",\"displayName\":\"传输线路-HARD_BAD\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"},{\"aEntity\":{\"entityTypeName\":\"专业\",\"entityName\":\"传输\",\"displayName\":\"传输\",\"props\":[]},\"zEntity\":{\"entityTypeName\":\"故障\",\"entityName\":\"传输线路\",\"displayName\":\"传输线路\",\"props\":[{\"attrCode\":\"rootalarm\",\"attrName\":\"根告警\",\"value\":\"\"}]},\"rsEntity\":{\"rsTypeName\":\"专业-故障\",\"rsName\":\"传输-传输线路\",\"displayName\":\"传输-传输线路\",\"props\":[]},\"tgName\":\"故障图谱,告警故障图谱,告警故障图谱,故障告警图谱\"}],\"pageNum\":1,\"pageSize\":10,\"size\":4,\"startRow\":0,\"endRow\":3,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"success\":true}";
        } else {
            Map<String, Object> params = new HashMap<>();
            params.put("entityName", "动环温湿度");
            params.put("tgName", "故障图谱/故障告警图谱");
            params.put("entityTypeName", "故障");
            params.put("pageNo", CommonConstant.PAGENUM);
            params.put("pageSize", CommonConstant.PAGESIZE);
            log.info("调用故障告警图谱接口获取到的故障根因URL:{},入参:{}", configProperties.getQueryCslineUrl(), JSONObject.toJSONString(params));
            apiResult = OkHttpUtils.doPost(configProperties.getQueryCslineUrl(), JSON.toJSONString(params));
            log.info("调用故障告警图谱接口获取到的故障根因URL:{}，返回内容:{}", configProperties.getQueryCslineUrl(), apiResult);
        }
        if (apiResult == null) {
            return ServiceResp.getInstance().error("调用故障告警图谱接口获取到的故障根因,返回数据异常");
        }
        JSONObject map = JSON.parseObject(apiResult);
        if (map == null || !map.getBoolean("success")) {
            return ServiceResp.getInstance().error("调用故障告警图谱接口获取到的故障根因,返回数据异常");
        }
        String fultName = "";
        JSONObject apiJson = map.getJSONObject("body");
        if (apiJson == null || apiJson.isEmpty()) {
            return ServiceResp.getInstance().error(2, CommonConstant.NO_FAULT_ROOTCAUSE);
        }
        JSONArray array = apiJson.getJSONArray("list");
        if (array == null || array.isEmpty()) {
            return ServiceResp.getInstance().error(2, CommonConstant.NO_FAULT_ROOTCAUSE);
        }
        String gzEntityName = "";
        String tempEarliestTime = "";
        for (Object obj : array) {
            JSONObject apiObj = JSON.parseObject(obj.toString());
            JSONObject zEntity = apiObj.getJSONObject("zEntity");
            for (Object esObj : esDatas) {
                JSONObject esJson = JSON.parseObject(esObj.toString()).getJSONObject("_source");
                if (esJson.getString("alarmTitle").contains(zEntity.getString("entityName"))) {
                    // 告警名称与传输根的告警名称
                    if (StringUtils.isBlank(tempEarliestTime)){
                        // 第一个时间
                        tempEarliestTime = esJson.getString("eventTime");
                        gzEntityName = zEntity.getString("displayName");
                    }else {
                        // 将tempEarliestTime转为时间进行比较
                        LocalDateTime tempEarliestDate = LocalDateTime.parse(tempEarliestTime, formatter);
                        LocalDateTime alarmEventTime = LocalDateTime.parse(esJson.getString("eventTime"), formatter);
                        if (alarmEventTime.isBefore(tempEarliestDate)){
                            // 当前匹配的告警时间更早
                            tempEarliestTime = esJson.getString("eventTime");
                            gzEntityName = zEntity.getString("displayName");
                        }
                    }
                }
            }
        }
        List<String> stationName = hnApiMapper.getStationName(stationCode);
        if (stationName != null && stationName.size() == 1){
            Map<String, Object> resultMap = new HashMap<>(2);
            resultMap.put("result", CommonConstant.RESULT_DH_JZ_TD_FAULT.replace("#stationName#",stationName.get(0)).replace("#neName#",evt.getNeName()));
            resultMap.put("fultName", gzEntityName);
            return ServiceResp.getInstance().success(resultMap);
        }
        return ServiceResp.getInstance().error(2, "无动环故障告警");
    }

    @Override
    public ServiceResp cloudCauseFault(ApiCloudQuery evt) throws Exception {
        String startTime = evt.getStartTime();
        Integer time = evt.getTime();
        List<String> pingAlarm = evt.getPingAlarm();
        List<String> downAlarm = evt.getDownAlarm();
        if (StringUtils.isBlank(startTime)){
            return ServiceResp.getInstance().error("参数错误：开始时间不能为空");
        }
        if (time == null){
            return ServiceResp.getInstance().error("参数错误：时间窗口参数不能为空");
        }
        if (pingAlarm == null || pingAlarm.isEmpty()){
            return ServiceResp.getInstance().error("参数错误：请设置pingAlarm参数");
        }
        if (downAlarm == null || downAlarm.isEmpty()){
            return ServiceResp.getInstance().error("参数错误：请设置downAlarm参数");
        }

        LocalDateTime startTimeDate = LocalDateTime.parse(startTime, formatter);
        String resultTime = startTimeDate.minusMinutes(time).format(formatter);

        // 构建dsl语句查询告警
        List allAlarm = new ArrayList<>();
        allAlarm.addAll(pingAlarm);
        allAlarm.addAll(downAlarm);
        String allAlarmStr = "\"" + String.join("\",\"", allAlarm) + "\"";

        String dsl = buildEsDslCloudRootCause(startTime, resultTime, CommonConstant.ALARM_STATUS,"["+allAlarmStr+"]");
        log.info("查询es库中告警Dsl:{}", dsl);
        EsHighClientUtil esHighClientUtil = new EsHighClientUtil();
        String tempEs = esHighClientUtil.dslHttp(alarmDataIndex, dsl, HttpMethod.POST.name(), esClient);
        log.info("查询es库中告警返回:{}", tempEs);
        JSONObject tempJson = JSON.parseObject(tempEs);
        JSONObject hits = tempJson.getJSONObject("hits");
        if (hits == null) {
            return ServiceResp.getInstance().error(2, CommonConstant.NO_FAULT_ROOTCAUSE);
        }
        Integer total = hits.getJSONObject("total").getInteger("value");
        if (total == null || total == 0) {
            return ServiceResp.getInstance().error(2, CommonConstant.NO_FAULT_ROOTCAUSE);
        }
        JSONArray esDatas = hits.getJSONArray("hits");
        if (esDatas == null || esDatas.isEmpty()) {
            return ServiceResp.getInstance().error(2, CommonConstant.NO_FAULT_ROOTCAUSE);
        }
        List<AlarmInfo> alarmInfos = new ArrayList<>();
        for (Object obj : esDatas) {
            // 遍历
            JSONObject sourceJSON = JSON.parseObject(obj.toString()).getJSONObject("_source");
            AlarmInfo alarmInfo = new AlarmInfo();
            alarmInfo.setAlarmTitle(sourceJSON.getString("alarmTitle"));
            //todo  确定ipaddress的值来源
//            JSONObject origSeverity = sourceJSON.getJSONObject("origSeverity");
//            String ipaddress = origSeverity.getString("ipaddress");
            alarmInfo.setIpaddress(sourceJSON.getString("ipaddress"));
            alarmInfo.setEquipmentName(sourceJSON.getString("equipmentName"));
            alarmInfos.add(alarmInfo);
        }
        // alarmInfos 去重ipaddress得到去重后的ipaddress集合
        List<String> ipList = alarmInfos.stream().map(AlarmInfo::getIpaddress).distinct().collect(Collectors.toList());
        if (ipList != null && ipList.size() == 1){
            // 只有一个ip地址,判断alarmTitle是否包含pingAlarm(优先)
            List<String> titles = alarmInfos.stream().map(AlarmInfo::getAlarmTitle).distinct().collect(Collectors.toList());
            if (titles != null && titles.size() > 0){
                boolean result = titles.stream().anyMatch(pingAlarm::contains);
                if (result){
                    // 逻辑1
                    Map<String, Object> resultMap = new HashMap<>(2);
                    // todo 一个ip应该对应一个设备吧,这样随便取一个就好
                    String equipmentName = alarmInfos.get(0).getEquipmentName();
                    resultMap.put("result", CommonConstant.RESULT_CLOUD_DW_FAULT.replace("#equipmentName#",equipmentName));
                    resultMap.put("fultName", CommonConstant.XNJ_EXCEPTION);
                    return ServiceResp.getInstance().success(resultMap);
                }else {
                    Map<String, Object> resultMap = new HashMap<>(2);
                    // todo 一个ip应该对应一个设备吧,这样随便取一个就好
                    String equipmentName = alarmInfos.get(0).getEquipmentName();
                    resultMap.put("result", CommonConstant.RESULT_CLOUD_JC_FAULT.replace("#equipmentName#",equipmentName));
                    resultMap.put("fultName", CommonConstant.JC_EXCEPTION);
                    return ServiceResp.getInstance().success(resultMap);
                }
            }
        }else {
            // 多个ip地址 逻辑2
            // 调用图谱能力接口查询
            String apiResult;
            Map<String, Object> params = new HashMap<>();
            for (String ip: ipList){
                params.put("entityName", ip);
                params.put("tgName", "资源图谱/业务平台-云");
                params.put("pageNo", CommonConstant.PAGENUM);
                params.put("pageSize", CommonConstant.PAGESIZE);
                log.info("调用资源图谱/业务平台-云图谱接口获取到的故障根因URL:{},入参:{}", configProperties.getQueryCslineUrl(), JSONObject.toJSONString(params));
                apiResult = OkHttpUtils.doPost(configProperties.getQueryCslineUrl(), JSON.toJSONString(params));
                log.info("调用故障告警图谱接口获取到的故障根因URL:{}，返回内容:{}", configProperties.getQueryCslineUrl(), apiResult);
                if (apiResult == null) {
                    return ServiceResp.getInstance().error("调用资源图谱/业务平台-云图谱接口获取到的故障根因,返回数据异常");
                }
                JSONObject map = JSON.parseObject(apiResult);
                if (map != null && map.getBoolean("success")) {
                    JSONObject apiJson = map.getJSONObject("body");
                    List<String> platForms = new ArrayList<>();
                    if (apiJson != null) {
                        JSONArray array = apiJson.getJSONArray("list");
                        if (array != null && !array.isEmpty()) {
                            for (Object obj : array) {
                                JSONObject jsonObject = JSON.parseObject(obj.toString());
                                JSONObject aEntity = jsonObject.getJSONObject("aEntity");
                                if (aEntity.getString("entityTypeName").equals(CommonConstant.PT)) {
                                    String entityName = aEntity.getString("entityName");
                                    if (StringUtils.isNotBlank(entityName)) {
                                        platForms.add(entityName);
                                    }
                                }
                            }
                        }
                    }
                    // 去重
                    List<String> distPlatForms = platForms.stream().distinct().collect(Collectors.toList());
                    if (distPlatForms != null && distPlatForms.size() >= 2) {
                        // 多个平台故障
                        Map<String, Object> resultMap = new HashMap<>(2);
                        String joinPlatForms = String.join(";", distPlatForms);
                        resultMap.put("result", CommonConstant.RESULT_CLOUD_PTS_DW_FAULT.replace("#platForms#", joinPlatForms));
                        resultMap.put("fultName", CommonConstant.DW_FAULT);
                        return ServiceResp.getInstance().success(resultMap);
                    }
                }
                    // 小于2
                    Map<String, Object> resultMap = new HashMap<>(2);
//                    String platForm = "";
//                    if (distPlatForms.size() == 1){
//                        platForm = distPlatForms.get(0);
//                    }else {
//                        return ServiceResp.getInstance().error("调用资源图谱/业务平台-云图谱接口获取到的故障根因,返回数据没有平台信息");
//                    }
                    String joinIps = String.join(";", ipList);
                    resultMap.put("result", CommonConstant.RESULT_CLOUD_PT_IPS_FAULT.replace("#ips#",joinIps));
                    resultMap.put("fultName", CommonConstant.PLATFORM_DW_FAULT);
                    return ServiceResp.getInstance().success(resultMap);

            }
        }
        return ServiceResp.getInstance().error(2, CommonConstant.NO_FAULT_ROOTCAUSE);
    }

    @Override
    public ServiceResp carrierFaultAnalysis(ApiCarrierQuery evt) throws Exception {
        // 参数校验
        if (StringUtils.isBlank(evt.getDeviceName()) && StringUtils.isBlank(evt.getDeviceIp())) {
            return ServiceResp.getInstance().error("参数错误：设备名称和设备IP不能同时为空");
        }
        if (StringUtils.isBlank(evt.getStartTime())) {
            return ServiceResp.getInstance().error("参数错误：告警发生时间不能为空");
        }
        if (evt.getOperationType() == null || (evt.getOperationType() != 0 && evt.getOperationType() != 1)) {
            return ServiceResp.getInstance().error("参数错误：操作类型只能为0或1");
        }
        if (StringUtils.isBlank(evt.getTgName())) {
            log.error("传入的tgName为null");
        }

        String stationCode = "";
        String stationName = "";

        // 1. 优先使用IP查询图谱能力获取局站编码
        if (StringUtils.isNotBlank(evt.getDeviceIp())) {
            StationQueryResponse stationResponse = queryStationByIp(evt.getDeviceIp(), evt.getTgName());
            if (stationResponse != null && stationResponse.getSuccess() &&
                stationResponse.getBody() != null && !stationResponse.getBody().isEmpty()) {
                StationQueryResponse.StationInfo stationInfo = stationResponse.getBody().get(0);
                stationCode = stationInfo.getStationCode();
                stationName = stationInfo.getStationName();
            }
        }

        // 2. 若用IP查询不到，则再使用设备名称查询
        if (StringUtils.isBlank(stationCode) && StringUtils.isNotBlank(evt.getDeviceName())) {
            StationQueryResponse stationResponse = queryStationByDeviceName(evt.getDeviceName(), evt.getTgName());
            if (stationResponse != null && stationResponse.getSuccess() &&
                stationResponse.getBody() != null && !stationResponse.getBody().isEmpty()) {
                StationQueryResponse.StationInfo stationInfo = stationResponse.getBody().get(0);
                stationCode = stationInfo.getStationCode();
                stationName = stationInfo.getStationName();
            }
        }

        // 3. 若IP和设备名称都查询不到，返回错误
        if (StringUtils.isBlank(stationCode)) {
            return ServiceResp.getInstance().error(2, CommonConstant.RESULT_NO_STATION_INFO);
        }

        // 4. 根据局站编码查询综告系统告警
        AlarmResponse alarmResponse = queryAlarmByStation(stationCode, evt.getStartTime());

        if (alarmResponse == null || !alarmResponse.getSuccess() ||
            alarmResponse.getBody() == null || alarmResponse.getBody().getRecords() == null ||
            alarmResponse.getBody().getRecords().isEmpty()) {
            // 未查询到告警数据
            Map<String, Object> resultMap = new HashMap<>();
            String result = CommonConstant.RESULT_CARRIER_NO_ALARM
                    .replace("#deviceName#", evt.getDeviceName())
                    .replace("#stationCode#", stationCode);
            resultMap.put("result", result);
            resultMap.put("fultName", CommonConstant.FAULT_NAME_TRANSMISSION_EQUIPMENT);
            return ServiceResp.getInstance().success(resultMap);
        }

        // 5. 处理查询到的告警数据
        return processAlarmData(evt, stationCode, stationName, alarmResponse);
    }

    /**
     * 根据设备IP查询局站信息
     */
    private StationQueryResponse queryStationByIp(String deviceIp, String tgName) {
        try {
            Map<String, Object> params = new HashMap<>();
            if (StringUtils.isNotBlank(tgName)) {
                params.put("tgName", tgName);
            }
            params.put("deviceIp", deviceIp);

            String url = configProperties.getQueryStationByIpUrl();
            if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
                // 测试数据
                String testResult = "{\"head\":{\"resTime\":\"2024-09-27 00:35:24\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":[{\"stationCode\":\"QHA.DFJ\",\"stationName\":\"东风\",\"stationId\":\"59000437\"}],\"success\":true}";
                return JSONObject.parseObject(testResult, StationQueryResponse.class);
            } else {
                log.info("根据设备IP查询局站信息URL:{},入参:{}", url, JSONObject.toJSONString(params));
                String apiResult = OkHttpUtils.doPost(url, JSON.toJSONString(params));
                log.info("根据设备IP查询局站信息返回:{}", apiResult);
                if (StringUtils.isNotBlank(apiResult)) {
                    return JSONObject.parseObject(apiResult, StationQueryResponse.class);
                }
            }
        } catch (Exception e) {
            log.error("根据设备IP查询局站信息异常", e);
        }
        return null;
    }

    /**
     * 根据设备名称查询局站信息
     */
    private StationQueryResponse queryStationByDeviceName(String deviceName, String tgName) {
        try {
            Map<String, Object> params = new HashMap<>();
            if (StringUtils.isNotBlank(tgName)) {
                params.put("tgName", tgName);
            }
            params.put("deviceName", deviceName);

            String url = configProperties.getQueryNenameUrl();
            if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
                // 测试数据
                String testResult = "{\"head\":{\"resTime\":\"2024-09-27 00:35:24\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"操作成功\"},\"body\":[{\"stationCode\":\"QHA.DFJ\",\"stationName\":\"东风\",\"stationId\":\"59000437\"}],\"success\":true}";
                return JSONObject.parseObject(testResult, StationQueryResponse.class);
            } else {
                log.info("根据设备名称查询局站信息URL:{},入参:{}", url, JSONObject.toJSONString(params));
                String apiResult = OkHttpUtils.doPost(url, JSON.toJSONString(params));
                log.info("根据设备名称查询局站信息返回:{}", apiResult);
                if (StringUtils.isNotBlank(apiResult)) {
                    return JSONObject.parseObject(apiResult, StationQueryResponse.class);
                }
            }
        } catch (Exception e) {
            log.error("根据设备名称查询局站信息异常", e);
        }
        return null;
    }

    /**
     * 根据局站编码查询综告系统告警
     */
    private AlarmResponse queryAlarmByStation(String stationCode, String startTime) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("equipmentname", stationCode);
            param.put("clearancereportflag", CommonConstant.CLEARANCE_REPORT_FLAG_DEFAULT);
            param.put("starttime", startTime);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("countTotal", "false");
            requestBody.put("pageNo", "0");
            requestBody.put("pageSize", "0");
            requestBody.put("param", param);

            String url = configProperties.getQueryAlarmUrl();
            if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
                // 测试数据
                String testResult = "{\"body\":{\"pageNo\":1,\"pageSize\":1000,\"total\":1,\"records\":[{\"alarmSrc\":{\"SRC_ALARM_ID\":\"1579636882013\"},\"specialty\":\"无线(中兴5G)\",\"alarmExt\":{\"ADDKEYWORD4\":\"NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维\",\"ADDKEYWORD3\":\"SubNetwork=460201,MEID=12607516,DLGNB=460-11_12607516,LCDU=8\"},\"clearancereportflag\":\"0\",\"equipmentname\":\"NR_ZTE_三亚蜈支洲岛江林村路口_AU2_3.5_SA_DX_B【室外-TT-TT-L1B】维\",\"relatedInfo\":[{\"PARENT_ID\":\"0\"}],\"originaleventtime\":\"2025-07-21 10:35:50\",\"firstalarmtime\":\"2025-07-21 10:35:50\",\"ocname\":\"HN3G_OC\",\"alarmobjectid\":\"2028237090\",\"locationinfo\":\"sbn=460201,NodeMe=12607516,Equipment=1,Rack=1,Shelf=1,Board=1\",\"additionaltext\":\"test\",\"faultid\":\"WL202507214062697\",\"alarmtitle\":\"DU小区退服\",\"alarminfo\":\"root1\",\"problemoccurrences\":\"1\",\"neTag\":{\"NE_ID\":\"12607516_1\"}}]},\"head\":{\"resTime\":\"1753066341240\",\"ticket\":null,\"respCode\":0,\"respMsg\":\"查询数据《成功》\"},\"success\":true}";
                return JSONObject.parseObject(testResult, AlarmResponse.class);
            } else {
                log.info("查询综告系统告警URL:{},入参:{}", url, JSONObject.toJSONString(requestBody));
                String apiResult = OkHttpUtils.doPost(url, JSON.toJSONString(requestBody));
                log.info("查询综告系统告警返回:{}", apiResult);
                if (StringUtils.isNotBlank(apiResult)) {
                    return JSONObject.parseObject(apiResult, AlarmResponse.class);
                }
            }
        } catch (Exception e) {
            log.error("查询综告系统告警异常", e);
        }
        return null;
    }

    /**
     * 处理告警数据并返回分析结果
     */
    private ServiceResp processAlarmData(ApiCarrierQuery evt, String stationCode, String stationName, AlarmResponse alarmResponse) {
        List<AlarmResponse.AlarmRecord> records = alarmResponse.getBody().getRecords();

        // 根据alarmSrc.SRC_ALARM_ID告警ID去重
        Map<String, AlarmResponse.AlarmRecord> uniqueAlarms = new HashMap<>();
        for (AlarmResponse.AlarmRecord record : records) {
            if (record.getAlarmSrc() != null && StringUtils.isNotBlank(record.getAlarmSrc().getSrcAlarmId())) {
                uniqueAlarms.put(record.getAlarmSrc().getSrcAlarmId(), record);
            }
        }

        // 根据专业和nacos配置的告警名称过滤
        List<AlarmResponse.AlarmRecord> filteredAlarms = filterAlarmsByConfig(uniqueAlarms.values(), evt.getSpecialty());

        if (filteredAlarms.isEmpty()) {
            // 过滤后无告警数据
            Map<String, Object> resultMap = new HashMap<>();
            String result = CommonConstant.RESULT_CARRIER_NO_ALARM
                    .replace("#deviceName#", evt.getDeviceName())
                    .replace("#stationCode#", stationCode);
            resultMap.put("result", result);
            resultMap.put("fultName", CommonConstant.FAULT_NAME_TRANSMISSION_EQUIPMENT);
            return ServiceResp.getInstance().success(resultMap);
        }

        // 根据operationType返回不同结果
        Map<String, Object> resultMap = new HashMap<>();
        if (evt.getOperationType() == 1) {
            // operationType=1时结果返回：停电
            StringBuilder resultBuilder = new StringBuilder();
            resultBuilder.append("根据资源承载图谱分析：")
                    .append(evt.getDeviceName())
                    .append("设备位于")
                    .append(stationName)
                    .append("，");

            for (AlarmResponse.AlarmRecord alarm : filteredAlarms) {
                resultBuilder.append("因")
                        .append(stationName)
                        .append("在")
                        .append(alarm.getFirstalarmtime())
                        .append("发生")
                        .append(alarm.getAlarmtitle())
                        .append("；");
            }

            resultBuilder.append("导致")
                    .append(evt.getDeviceName())
                    .append("发生故障；");

            resultMap.put("result", resultBuilder.toString());
            resultMap.put("fultName", CommonConstant.FAULT_NAME_POWER_OUTAGE);
        } else {
            // operationType=0时结果返回：动环故障
            String result = CommonConstant.RESULT_CARRIER_ENVIRONMENT_FAULT
                    .replace("#deviceName#", evt.getDeviceName())
                    .replace("#stationName#", stationName)
                    .replace("#alarmTime#", filteredAlarms.get(0).getFirstalarmtime());

            resultMap.put("result", result);
            resultMap.put("fultName", CommonConstant.FAULT_NAME_ENVIRONMENT_FAULT);
        }

        return ServiceResp.getInstance().success(resultMap);
    }

    /**
     * 根据专业和nacos配置的告警名称过滤告警
     */
    private List<AlarmResponse.AlarmRecord> filterAlarmsByConfig(Collection<AlarmResponse.AlarmRecord> alarms, String specialty) {
        List<AlarmResponse.AlarmRecord> result = new ArrayList<>();

        // 若nacos未配置告警名称及传参专业为空，则不做过滤处理取全量告警
        if (alarmNameConfig.getNames() == null || alarmNameConfig.getNames().isEmpty() || StringUtils.isBlank(specialty)) {
            result.addAll(alarms);
            return result;
        }

        // 获取该专业配置的告警名称列表
        List<String> configuredAlarmNames = alarmNameConfig.getNames().get(specialty);
        if (configuredAlarmNames == null || configuredAlarmNames.isEmpty()) {
            // 该专业未配置告警名称，取全量告警
            result.addAll(alarms);
            return result;
        }

        // 根据配置的告警名称过滤
        for (AlarmResponse.AlarmRecord alarm : alarms) {
            if (StringUtils.isNotBlank(alarm.getAlarmtitle())) {
                for (String configuredName : configuredAlarmNames) {
                    if (alarm.getAlarmtitle().contains(configuredName)) {
                        result.add(alarm);
                        break;
                    }
                }
            }
        }

        return result;
    }

    /**
     * 传输设备告警查询
     */
    @Override
    public ServiceResp transmissionAlarmQuery(ApiTransmissionAlarmQuery evt) throws Exception {
        log.info("传输设备告警查询开始，参数：{}", JSONObject.toJSONString(evt));

        // 参数校验
        if (StringUtils.isBlank(evt.getDeviceName()) || StringUtils.isBlank(evt.getStartTime()) ||
            evt.getSearchType() == null || evt.getOperationType() == null) {
            return ServiceResp.getInstance().error("参数不完整");
        }

        if (evt.getSearchType() == 0) {
            // searchType=0: 图谱查询对端设备
            return queryPeerDeviceByGraph(evt);
        } else if (evt.getSearchType() == 1) {
            // searchType=1: 直接查询传输设备告警，不需要先查图谱
            return queryTransmissionAlarmDirect(evt);
        } else {
            return ServiceResp.getInstance().error("searchType参数错误");
        }
    }

    /**
     * 图谱查询对端设备
     */
    private ServiceResp queryPeerDeviceByGraph(ApiTransmissionAlarmQuery evt) throws Exception {
        log.info("开始图谱查询对端设备，参数：{}", JSONObject.toJSONString(evt));

        PeerDeviceResponse peerDeviceResponse = null;

        // 1. 优先使用IP查询图谱能力获取对端设备
        if (StringUtils.isNotBlank(evt.getDeviceIp())) {
            peerDeviceResponse = queryPeerDeviceByIp(evt.getDeviceIp(), evt.getTgName());
        }

        // 2. 若用IP为空或查询不到，则再使用设备名称查询
        if ((peerDeviceResponse == null || !peerDeviceResponse.getSuccess() ||
             peerDeviceResponse.getBody() == null || peerDeviceResponse.getBody().getRecords() == null ||
             peerDeviceResponse.getBody().getRecords().isEmpty()) && StringUtils.isNotBlank(evt.getDeviceName())) {
            peerDeviceResponse = queryPeerDeviceByName(evt.getDeviceName(), evt.getTgName());
        }

        // 3. 若IP和设备名称都查询不到，respCode返回2，respMsg返回：无对端设备信息
        if (peerDeviceResponse == null || !peerDeviceResponse.getSuccess() ||
            peerDeviceResponse.getBody() == null || peerDeviceResponse.getBody().getRecords() == null ||
            peerDeviceResponse.getBody().getRecords().isEmpty()) {
            return ServiceResp.getInstance().error(2, CommonConstant.RESULT_NO_PEER_DEVICE);
        }

        // 返回对端设备信息
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("deviceInfo", peerDeviceResponse.getBody().getRecords());
        return ServiceResp.getInstance().success(resultMap);
    }

    /**
     * 直接查询传输设备告警（searchType=1）
     */
    private ServiceResp queryTransmissionAlarmDirect(ApiTransmissionAlarmQuery evt) throws Exception {
        log.info("开始直接查询传输设备告警，参数：{}", JSONObject.toJSONString(evt));

        // 直接根据入参查询综告系统告警，优先使用IP，如果IP为空则使用设备名称
        String equipmentName = StringUtils.isNotBlank(evt.getDeviceIp()) ? evt.getDeviceIp() : evt.getDeviceName();

        AlarmResponse alarmResponse = queryAlarmByEquipmentName(equipmentName, evt.getStartTime());

        if (alarmResponse == null || !alarmResponse.getSuccess() ||
            alarmResponse.getBody() == null || alarmResponse.getBody().getRecords() == null ||
            alarmResponse.getBody().getRecords().isEmpty()) {
            // 无告警数据
            return buildTransmissionNoAlarmResult(evt.getDeviceName(), evt.getDeviceName(), evt.getDeviceType());
        }

        // 处理告警数据
        return processTransmissionAlarmData(evt, evt.getDeviceName(), evt.getDeviceType(), alarmResponse);
    }

    /**
     * 查询传输设备告警（原方法，保留用于图谱查询后的告警查询）
     */
    private ServiceResp queryTransmissionAlarm(ApiTransmissionAlarmQuery evt) throws Exception {
        log.info("开始查询传输设备告警，参数：{}", JSONObject.toJSONString(evt));

        // 首先获取对端设备信息
        PeerDeviceResponse peerDeviceResponse = null;

        // 优先使用IP查询
        if (StringUtils.isNotBlank(evt.getDeviceIp())) {
            peerDeviceResponse = queryPeerDeviceByIp(evt.getDeviceIp(), evt.getTgName());
        }

        // IP查询不到则使用设备名称查询
        if ((peerDeviceResponse == null || !peerDeviceResponse.getSuccess() ||
             peerDeviceResponse.getBody() == null || peerDeviceResponse.getBody().getRecords() == null ||
             peerDeviceResponse.getBody().getRecords().isEmpty()) && StringUtils.isNotBlank(evt.getDeviceName())) {
            peerDeviceResponse = queryPeerDeviceByName(evt.getDeviceName(), evt.getTgName());
        }

        // 无对端设备信息
        if (peerDeviceResponse == null || !peerDeviceResponse.getSuccess() ||
            peerDeviceResponse.getBody() == null || peerDeviceResponse.getBody().getRecords() == null ||
            peerDeviceResponse.getBody().getRecords().isEmpty()) {
            return ServiceResp.getInstance().error(2, CommonConstant.RESULT_NO_PEER_DEVICE);
        }

        // 获取第一个对端设备信息
        PeerDeviceResponse.PeerDeviceInfo peerDevice = peerDeviceResponse.getBody().getRecords().get(0);
        String peerDeviceName = peerDevice.getDeviceName();
        String peerDeviceIp = peerDevice.getDeviceIp();
        String deviceType = StringUtils.isNotBlank(evt.getDeviceType()) ? evt.getDeviceType() :
                           (StringUtils.isNotBlank(peerDevice.getDeviceType()) ? peerDevice.getDeviceType() : "传输设备");

        // 根据对端设备IP或设备名称查询综告系统告警
        AlarmResponse alarmResponse = queryAlarmByPeerDevice(peerDeviceIp, peerDeviceName, evt.getStartTime());

        if (alarmResponse == null || !alarmResponse.getSuccess() ||
            alarmResponse.getBody() == null || alarmResponse.getBody().getRecords() == null ||
            alarmResponse.getBody().getRecords().isEmpty()) {
            // 未查询到告警数据
            return buildTransmissionNoAlarmResult(evt.getDeviceName(), peerDeviceName, deviceType);
        }

        // 处理查询到的告警数据
        return processTransmissionAlarmData(evt, peerDeviceName, deviceType, alarmResponse);
    }

    /**
     * 根据IP查询对端设备
     */
    private PeerDeviceResponse queryPeerDeviceByIp(String deviceIp, String tgName) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("deviceIp", deviceIp);
            params.put("tgName", tgName);

            String url = configProperties.getQueryPeerDeviceUrl();
            if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
                // 测试数据
                String testResult = buildTestPeerDeviceResponse();
                return JSONObject.parseObject(testResult, PeerDeviceResponse.class);
            } else {
                log.info("根据IP查询对端设备URL:{},入参:{}", url, JSONObject.toJSONString(params));
                String apiResult = OkHttpUtils.doPost(url, JSON.toJSONString(params));
                log.info("根据IP查询对端设备返回:{}", apiResult);
                if (StringUtils.isNotBlank(apiResult)) {
                    return JSONObject.parseObject(apiResult, PeerDeviceResponse.class);
                }
            }
        } catch (Exception e) {
            log.error("根据IP查询对端设备异常", e);
        }
        return null;
    }

    /**
     * 根据设备名称查询对端设备
     */
    private PeerDeviceResponse queryPeerDeviceByName(String deviceName, String tgName) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("deviceName", deviceName);
            params.put("tgName", tgName);

            String url = configProperties.getQueryPeerDeviceUrl();
            if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
                // 测试数据
                String testResult = buildTestPeerDeviceResponse();
                return JSONObject.parseObject(testResult, PeerDeviceResponse.class);
            } else {
                log.info("根据设备名称查询对端设备URL:{},入参:{}", url, JSONObject.toJSONString(params));
                String apiResult = OkHttpUtils.doPost(url, JSON.toJSONString(params));
                log.info("根据设备名称查询对端设备返回:{}", apiResult);
                if (StringUtils.isNotBlank(apiResult)) {
                    return JSONObject.parseObject(apiResult, PeerDeviceResponse.class);
                }
            }
        } catch (Exception e) {
            log.error("根据设备名称查询对端设备异常", e);
        }
        return null;
    }

    /**
     * 根据设备名称查询综告系统告警
     */
    private AlarmResponse queryAlarmByEquipmentName(String equipmentName, String startTime) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("equipmentname", equipmentName);
            param.put("clearancereportflag", "0"); // 告警状态为0
            param.put("starttime", startTime);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("countTotal", "false");
            requestBody.put("pageNo", "0");
            requestBody.put("pageSize", "0");
            requestBody.put("param", param);

            String url = configProperties.getQueryAlarmUrl();
            if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
                // 测试数据
                String testResult = buildTestAlarmResponse();
                return JSONObject.parseObject(testResult, AlarmResponse.class);
            } else {
                log.info("查询设备告警URL:{},入参:{}", url, JSONObject.toJSONString(requestBody));
                String apiResult = OkHttpUtils.doPost(url, JSON.toJSONString(requestBody));
                log.info("查询设备告警返回:{}", apiResult);
                if (StringUtils.isNotBlank(apiResult)) {
                    return JSONObject.parseObject(apiResult, AlarmResponse.class);
                }
            }
        } catch (Exception e) {
            log.error("查询设备告警异常", e);
        }
        return null;
    }

    /**
     * 根据对端设备查询综告系统告警
     */
    private AlarmResponse queryAlarmByPeerDevice(String peerDeviceIp, String peerDeviceName, String startTime) {
        try {
            // 优先使用IP查询，如果IP为空则使用设备名称
            String equipmentName = StringUtils.isNotBlank(peerDeviceIp) ? peerDeviceIp : peerDeviceName;

            Map<String, Object> param = new HashMap<>();
            param.put("equipmentname", equipmentName);
            param.put("clearancereportflag", "0"); // 告警状态为0
            param.put("starttime", startTime);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("countTotal", "false");
            requestBody.put("pageNo", "0");
            requestBody.put("pageSize", "0");
            requestBody.put("param", param);

            String url = configProperties.getQueryAlarmUrl();
            if (CommonConstant.IS_TEST.equals(configProperties.getIfTest())) {
                // 测试数据
                String testResult = buildTestAlarmResponse();
                return JSONObject.parseObject(testResult, AlarmResponse.class);
            } else {
                log.info("查询对端设备告警URL:{},入参:{}", url, JSONObject.toJSONString(requestBody));
                String apiResult = OkHttpUtils.doPost(url, JSON.toJSONString(requestBody));
                log.info("查询对端设备告警返回:{}", apiResult);
                if (StringUtils.isNotBlank(apiResult)) {
                    return JSONObject.parseObject(apiResult, AlarmResponse.class);
                }
            }
        } catch (Exception e) {
            log.error("查询对端设备告警异常", e);
        }
        return null;
    }

    /**
     * 处理传输设备告警数据
     */
    private ServiceResp processTransmissionAlarmData(ApiTransmissionAlarmQuery evt, String peerDeviceName,
                                                   String deviceType, AlarmResponse alarmResponse) {
        // 根据alarmSrc.SRC_ALARM_ID告警ID去重
        Map<String, AlarmResponse.AlarmRecord> uniqueAlarms = new LinkedHashMap<>();
        for (AlarmResponse.AlarmRecord alarm : alarmResponse.getBody().getRecords()) {
            if (alarm.getAlarmSrc() != null && StringUtils.isNotBlank(alarm.getAlarmSrc().getSrcAlarmId())) {
                uniqueAlarms.put(alarm.getAlarmSrc().getSrcAlarmId(), alarm);
            }
        }

        // 根据专业和nacos配置的告警名称过滤
        List<AlarmResponse.AlarmRecord> filteredAlarms = filterAlarmsByConfig(uniqueAlarms.values(), evt.getSpecialty());

        if (filteredAlarms.isEmpty()) {
            // 过滤后无告警数据
            return buildTransmissionNoAlarmResult(evt.getDeviceName(), peerDeviceName, deviceType);
        }

        // 构建返回结果
        Map<String, Object> resultMap = new HashMap<>();
        StringBuilder resultBuilder = new StringBuilder();

        // 根据operationType构建不同的返回结果
        if (evt.getOperationType() == 1) {
            // operationType=1时结果返回具体故障类型
            for (AlarmResponse.AlarmRecord alarm : filteredAlarms) {
                String alarmResult = CommonConstant.RESULT_TRANSMISSION_SPECIFIC_FAULT
                        .replace("#deviceName#", evt.getDeviceName())
                        .replace("#deviceType#", deviceType)
                        .replace("#peerDeviceName#", peerDeviceName)
                        .replace("#alarmTime#", alarm.getFirstalarmtime())
                        .replace("#alarmTitle#", alarm.getAlarmtitle());
                resultBuilder.append(alarmResult);
            }

            // 确定故障类型
            String faultName = determineFaultType(filteredAlarms);
            resultMap.put("fultName", faultName);
        } else {
            // operationType=0时结果返回传输专业故障
            for (AlarmResponse.AlarmRecord alarm : filteredAlarms) {
                String alarmResult = CommonConstant.RESULT_TRANSMISSION_PROFESSIONAL_FAULT
                        .replace("#deviceName#", evt.getDeviceName())
                        .replace("#deviceType#", deviceType)
                        .replace("#peerDeviceName#", peerDeviceName)
                        .replace("#alarmTime#", alarm.getFirstalarmtime());
                resultBuilder.append(alarmResult);
            }
            resultMap.put("fultName", CommonConstant.FAULT_NAME_TRANSMISSION_PROFESSIONAL);
        }

        resultMap.put("result", resultBuilder.toString());
        return ServiceResp.getInstance().success(resultMap);
    }

    /**
     * 构建无告警数据的返回结果
     */
    private ServiceResp buildTransmissionNoAlarmResult(String deviceName, String peerDeviceName, String deviceType) {
        Map<String, Object> resultMap = new HashMap<>();
        String result = CommonConstant.RESULT_TRANSMISSION_NO_ALARM
                .replace("#deviceName#", deviceName)
                .replace("#deviceType#", deviceType)
                .replace("#peerDeviceName#", peerDeviceName);
        resultMap.put("result", result);
        resultMap.put("fultName", CommonConstant.FAULT_NAME_NEXT_CHECK_DATA_EQUIPMENT);
        return ServiceResp.getInstance().success(resultMap);
    }

    /**
     * 根据告警确定故障类型
     */
    private String determineFaultType(List<AlarmResponse.AlarmRecord> alarms) {
        // 检查是否有光缆中断类型的告警（1,2,3类型）
        boolean hasCableInterruption = false;
        // 检查是否有板卡故障类型的告警（4,5类型）
        boolean hasBoardFault = false;

        for (AlarmResponse.AlarmRecord alarm : alarms) {
            if (StringUtils.isNotBlank(alarm.getAlarmtitle())) {
                // 根据nacos配置的告警名称来判断故障类型
                if (isCableInterruptionAlarm(alarm.getAlarmtitle())) {
                    hasCableInterruption = true;
                }
                if (isBoardFaultAlarm(alarm.getAlarmtitle())) {
                    hasBoardFault = true;
                }
            }
        }

        // 如果1,2,3和4,5两个告警名称同时被匹配到，优先级最高的是光缆中断
        if (hasCableInterruption) {
            return CommonConstant.FAULT_NAME_CABLE_INTERRUPTION;
        } else if (hasBoardFault) {
            return CommonConstant.FAULT_NAME_BOARD_FAULT;
        } else {
            // 默认返回传输专业故障
            return CommonConstant.FAULT_NAME_TRANSMISSION_PROFESSIONAL;
        }
    }

    /**
     * 判断是否为光缆中断类型告警
     */
    private boolean isCableInterruptionAlarm(String alarmTitle) {
        // 根据nacos配置的传输专业告警名称判断
        if (alarmNameConfig.getTransmission() != null &&
            alarmNameConfig.getTransmission().getCableInterruption() != null) {
            List<String> cableInterruptionAlarms = alarmNameConfig.getTransmission().getCableInterruption();
            for (String alarmName : cableInterruptionAlarms) {
                if (StringUtils.isNotBlank(alarmName) && alarmTitle.contains(alarmName)) {
                    return true;
                }
            }
        }
        // 默认判断逻辑
        return alarmTitle.contains("光缆") || alarmTitle.contains("中断") || alarmTitle.contains("断纤") ||
               alarmTitle.contains("光纤") || alarmTitle.contains("链路故障");
    }

    /**
     * 判断是否为板卡故障类型告警
     */
    private boolean isBoardFaultAlarm(String alarmTitle) {
        // 根据nacos配置的传输专业告警名称判断
        if (alarmNameConfig.getTransmission() != null &&
            alarmNameConfig.getTransmission().getBoardFault() != null) {
            List<String> boardFaultAlarms = alarmNameConfig.getTransmission().getBoardFault();
            for (String alarmName : boardFaultAlarms) {
                if (StringUtils.isNotBlank(alarmName) && alarmTitle.contains(alarmName)) {
                    return true;
                }
            }
        }
        // 默认判断逻辑
        return alarmTitle.contains("板卡") || alarmTitle.contains("硬件") || alarmTitle.contains("设备故障") ||
               alarmTitle.contains("设备异常");
    }

    /**
     * 构建测试用的对端设备响应数据
     */
    private String buildTestPeerDeviceResponse() {
        return "{\n" +
                "  \"head\": {\n" +
                "    \"resTime\": \"" + System.currentTimeMillis() + "\",\n" +
                "    \"ticket\": null,\n" +
                "    \"respCode\": 0,\n" +
                "    \"respMsg\": \"查询成功\"\n" +
                "  },\n" +
                "  \"body\": {\n" +
                "    \"pageNo\": 1,\n" +
                "    \"pageSize\": 10,\n" +
                "    \"total\": 1,\n" +
                "    \"records\": [\n" +
                "      {\n" +
                "        \"deviceName\": \"测试对端设备\",\n" +
                "        \"deviceIp\": \"*************\",\n" +
                "        \"deviceType\": \"传输设备\",\n" +
                "        \"deviceId\": \"TEST001\",\n" +
                "        \"entityName\": \"测试对端设备\",\n" +
                "        \"entityType\": \"TRANSMISSION\",\n" +
                "        \"entityId\": \"TEST001\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"success\": true\n" +
                "}";
    }

    /**
     * 构建测试用的告警响应数据
     */
    private String buildTestAlarmResponse() {
        return "{\n" +
                "  \"head\": {\n" +
                "    \"resTime\": \"" + System.currentTimeMillis() + "\",\n" +
                "    \"ticket\": null,\n" +
                "    \"respCode\": 0,\n" +
                "    \"respMsg\": \"查询数据成功\"\n" +
                "  },\n" +
                "  \"body\": {\n" +
                "    \"pageNo\": 1,\n" +
                "    \"pageSize\": 1000,\n" +
                "    \"total\": 1,\n" +
                "    \"records\": [\n" +
                "      {\n" +
                "        \"alarmSrc\": {\n" +
                "          \"SRC_ALARM_ID\": \"TEST123456\"\n" +
                "        },\n" +
                "        \"specialty\": \"传输\",\n" +
                "        \"clearancereportflag\": \"0\",\n" +
                "        \"equipmentname\": \"测试传输设备\",\n" +
                "        \"firstalarmtime\": \"2025-01-01 10:00:00\",\n" +
                "        \"alarmtitle\": \"光缆中断\",\n" +
                "        \"alarminfo\": \"测试告警\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"success\": true\n" +
                "}";
    }
}
