package com.ffcs.oss.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName MyConfigProperties
 * @Description TODO
 * <AUTHOR>
 */
@Configuration
@Data
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "hnconfig", ignoreUnknownFields = false)
public class HnConfigProperties {
    private String ifTest;
    private String queryNenameUrl;
    // 传输线路故障告警图谱
    private String queryCslineUrl;
    // 动环温湿度故障告警图谱
    private String queryDhwdUrl;
    // 图谱能力查询设备IP对应局站编码的URL
    private String queryStationByIpUrl;
    // 综告系统查询告警的URL
    private String queryAlarmUrl;
}
